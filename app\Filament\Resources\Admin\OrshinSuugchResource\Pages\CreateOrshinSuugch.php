<?php

namespace App\Filament\Resources\Admin\OrshinSuugchResource\Pages;

use App\Models\OrshinSuugch;
use App\Services\ToolService;
use App\Services\BPayService;
use App\Services\UserInfoService;
use App\Filament\Resources\Admin\OrshinSuugchResource;
use Filament\Resources\Pages\CreateRecord;

class CreateOrshinSuugch extends CreateRecord
{
    protected static string $resource = OrshinSuugchResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data[OrshinSuugch::IS_ADMIN] = true;
        return parent::mutateFormDataBeforeCreate($data);
    }


    protected function afterCreate(): void
    {
        $service      = resolve(UserInfoService::class);
        $sukh         = $service->getAUSukh();

        if (!$sukh) {
            throw new \Exception('No Sukh found for the authenticated user. Please ensure the user is associated with a Sukh.');
        }

        $orshinSuugch = $this->record;
        $orshinSuugch->sukhs()->save($sukh);

        // $orshinSuugch->uniq_code = resolve(ToolService::class)->generateCodeFromDatetime();
        // resolve(BPayService::class)->createBpayUser($orshinSuugch);
    }
}
