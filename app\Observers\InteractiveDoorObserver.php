<?php

namespace App\Observers;

use App\Models\Orc;
use App\Models\Korpus;
use App\Services\InteractiveDoorManagementService;
use Illuminate\Support\Facades\Log;

/**
 * Observer for automatic floor and door generation in the interactive door system
 */
class InteractiveDoorObserver
{
    protected InteractiveDoorManagementService $doorService;

    public function __construct(InteractiveDoorManagementService $doorService)
    {
        $this->doorService = $doorService;
    }

    /**
     * Handle Orc created event - automatically generate floors
     */
    public function orcCreated(Orc $orc): void
    {
        Log::info('InteractiveDoorObserver: Orc created, generating floors', [
            'orc_id' => $orc->id,
            'korpus_id' => $orc->korpus_id
        ]);

        try {
            $this->doorService->generateFloorsForOrc($orc);
        } catch (\Exception $e) {
            Log::error('InteractiveDoorObserver: Failed to generate floors for new Orc', [
                'orc_id' => $orc->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle Korpus updated event - regenerate doors if configuration changed
     */
    public function korpusUpdated(Korpus $korpus): void
    {
        // Check if door-related configuration was changed
        $relevantFields = ['numbering_type', 'digit_multiplier', 'floors_count', 'doors_per_floor'];
        $changedFields = array_keys($korpus->getChanges());
        $hasRelevantChanges = !empty(array_intersect($relevantFields, $changedFields));

        if ($hasRelevantChanges) {
            Log::info('InteractiveDoorObserver: Korpus door configuration updated', [
                'korpus_id' => $korpus->id,
                'changed_fields' => $changedFields
            ]);

            try {
                // If floors_count or doors_per_floor changed, update existing floors
                if (in_array('floors_count', $changedFields) || in_array('doors_per_floor', $changedFields)) {
                    $this->updateExistingFloorsConfiguration($korpus);
                }

                // If numbering_type or digit_multiplier changed, regenerate door numbers
                if (in_array('numbering_type', $changedFields) || in_array('digit_multiplier', $changedFields)) {
                    $this->doorService->generateDoorNumbers($korpus, true);
                }
            } catch (\Exception $e) {
                Log::error('InteractiveDoorObserver: Failed to update doors after Korpus configuration change', [
                    'korpus_id' => $korpus->id,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Update existing floors configuration when Korpus defaults change
     */
    protected function updateExistingFloorsConfiguration(Korpus $korpus): void
    {
        foreach ($korpus->orcs as $orc) {
            // Only update Orcs that don't have custom floor count override
            if (!$orc->floors_count_override) {
                $this->doorService->updateOrcFloorsCount($orc, $korpus->floors_count);
            }

            // Update doors_per_floor for all floors that use default
            foreach ($orc->davhars as $davhar) {
                // Update if the floor is using the default doors count
                if ($davhar->doors_count == $korpus->getOriginal('doors_per_floor')) {
                    $this->doorService->updateFloorDoorsCount($davhar, $korpus->doors_per_floor);
                }
            }
        }
    }
}
