<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Form Section -->
        <x-filament::section>
            <x-slot name="heading">
                Блок сонгох
            </x-slot>
            
            {{ $this->form }}
        </x-filament::section>

        <!-- Statistics Section -->
        @if($this->getSelectedKorpus() && $this->getStatistics())
            <x-filament::section>
                <x-slot name="heading">
                    Статистик мэдээлэл
                </x-slot>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-primary-50 dark:bg-primary-900/20 p-4 rounded-lg">
                        <div class="text-sm font-medium text-primary-600 dark:text-primary-400">
                            Дугаарлалтын төрөл
                        </div>
                        <div class="text-2xl font-bold text-primary-900 dark:text-primary-100">
                            @switch($this->getStatistics()['numbering_type'])
                                @case(1)
                                    Төрөл 1: Б<PERSON><PERSON><PERSON><PERSON><PERSON>
                                    @break
                                @case(2)
                                    Төрөл 2: Орцны
                                    @break
                                @case(3)
                                    Төрөл 3: Давхрын
                                    @break
                                @default
                                    Тодорхойгүй
                            @endswitch
                        </div>
                    </div>

                    <div class="bg-success-50 dark:bg-success-900/20 p-4 rounded-lg">
                        <div class="text-sm font-medium text-success-600 dark:text-success-400">
                            Орцны тоо
                        </div>
                        <div class="text-2xl font-bold text-success-900 dark:text-success-100">
                            {{ $this->getStatistics()['total_orcs'] }}
                        </div>
                    </div>

                    <div class="bg-warning-50 dark:bg-warning-900/20 p-4 rounded-lg">
                        <div class="text-sm font-medium text-warning-600 dark:text-warning-400">
                            Давхрын тоо
                        </div>
                        <div class="text-2xl font-bold text-warning-900 dark:text-warning-100">
                            {{ $this->getStatistics()['total_davhars'] }}
                        </div>
                    </div>

                    <div class="bg-info-50 dark:bg-info-900/20 p-4 rounded-lg">
                        <div class="text-sm font-medium text-info-600 dark:text-info-400">
                            Хаалганы тоо
                        </div>
                        <div class="text-2xl font-bold text-info-900 dark:text-info-100">
                            {{ $this->getStatistics()['total_toots'] }} / {{ $this->getStatistics()['expected_toots'] }}
                        </div>
                        <div class="text-xs text-info-600 dark:text-info-400">
                            ({{ $this->getStatistics()['completion_percentage'] }}% бүрэн)
                        </div>
                    </div>
                </div>
            </x-filament::section>

            <!-- Korpus Details Section -->
            <x-filament::section>
                <x-slot name="heading">
                    Блокийн дэлгэрэнгүй мэдээлэл
                </x-slot>
                
                <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Блокийн нэр
                            </label>
                            <div class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                {{ $this->getSelectedKorpus()->name }}
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Хаалганы муж
                            </label>
                            <div class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                {{ $this->getSelectedKorpus()->begin_toot_number }}-{{ $this->getSelectedKorpus()->end_toot_number }}
                            </div>
                        </div>

                        @if($this->getSelectedKorpus()->numbering_type == 3)
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Цифрийн формат
                                </label>
                                <div class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                    @switch($this->getSelectedKorpus()->digit_multiplier)
                                        @case(10)
                                            2 оронтой (11, 12, 13...)
                                            @break
                                        @case(100)
                                            3 оронтой (101, 102, 103...)
                                            @break
                                        @case(1000)
                                            4 оронтой (1001, 1002, 1003...)
                                            @break
                                        @case(10000)
                                            5 оронтой (10001, 10002, 10003...)
                                            @break
                                        @default
                                            Тодорхойгүй
                                    @endswitch
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Progress Bar -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Хаалганы дугаарлалтын явц
                        </label>
                        <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                            <div class="bg-blue-600 h-2.5 rounded-full" style="width: {{ $this->getStatistics()['completion_percentage'] }}%"></div>
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                            {{ $this->getStatistics()['completion_percentage'] }}% бүрэн 
                            ({{ $this->getStatistics()['total_toots'] }}/{{ $this->getStatistics()['expected_toots'] }} хаалга)
                        </div>
                    </div>
                </div>
            </x-filament::section>

            <!-- Quick Actions Section -->
            <x-filament::section>
                <x-slot name="heading">
                    Хурдан үйлдлүүд
                </x-slot>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">
                            Автомат дугаарлалт
                        </h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                            Блокийн тохиргоонд тулгуурлан автоматаар хаалганы дугаар үүсгэнэ.
                        </p>
                        <x-filament::button 
                            wire:click="generateAllDoors"
                            color="success"
                            size="sm"
                        >
                            Дугаар үүсгэх
                        </x-filament::button>
                    </div>

                    <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">
                            Тохиргоо шалгах
                        </h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                            Блокийн дугаарлалтын тохиргоо зөв эсэхийг шалгана.
                        </p>
                        <x-filament::button 
                            wire:click="validateConfiguration"
                            color="info"
                            size="sm"
                        >
                            Шалгах
                        </x-filament::button>
                    </div>

                    <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">
                            Гарын авлагын дугаарлалт
                        </h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                            Хаалганы дугаарыг гараар тохируулах бол "Хаалганууд" табыг ашиглана уу.
                        </p>
                        <x-filament::button 
                            color="warning"
                            size="sm"
                            disabled
                        >
                            Хаалганууд табыг үзэх
                        </x-filament::button>
                    </div>
                </div>
            </x-filament::section>
        @else
            <x-filament::section>
                <div class="text-center py-8">
                    <div class="text-gray-500 dark:text-gray-400">
                        <svg class="mx-auto h-12 w-12 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                        </svg>
                        <h3 class="text-lg font-medium mb-2">Блок сонгоно уу</h3>
                        <p class="text-sm">
                            Хаалганы дугаарлалтын удирдлагыг эхлүүлэхийн тулд эхлээд байр, дараа нь блок сонгоно уу.
                        </p>
                    </div>
                </div>
            </x-filament::section>
        @endif
    </div>
</x-filament-panels::page>
