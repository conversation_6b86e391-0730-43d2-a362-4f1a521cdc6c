<?php

namespace App\Filament\Resources\Admin\BairResource\RelationManagers;

use App\Models\Toot;
use App\Models\Davhar;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class TootsRelationManager extends RelationManager
{
    protected static string $relationship = 'toots';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('korpus_id')
                    ->label('Блок')
                    ->relationship('korpus', 'name')
                    ->required()
                    ->live()
                    ->afterStateUpdated(fn (callable $set) => $set('davhar_id', null)),

                Forms\Components\Select::make('davhar_id')
                    ->label('Давхар')
                    ->options(function (callable $get) {
                        $korpusId = $get('korpus_id');
                        if (!$korpusId) return [];

                        return Davhar::whereHas('orc', function ($query) use ($korpusId) {
                            $query->where('korpus_id', $korpusId);
                        })->get()->mapWithKeys(function ($davhar) {
                            return [$davhar->id => "Орц {$davhar->orc->number} - Давхар {$davhar->number}"];
                        });
                    })
                    ->required()
                    ->live(),

                Forms\Components\TextInput::make('number')
                    ->label('Хаалганы дугаар')
                    ->numeric()
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->helperText('Хаалганы дугаарыг оруулна уу'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('number')
            ->columns([
                Tables\Columns\TextColumn::make('number')
                    ->label('Хаалганы дугаар')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('korpus.name')
                    ->label('Блок')
                    ->sortable(),

                Tables\Columns\TextColumn::make('davhar.orc.number')
                    ->label('Орц')
                    ->sortable(),

                Tables\Columns\TextColumn::make('davhar.number')
                    ->label('Давхар')
                    ->sortable(),

                Tables\Columns\TextColumn::make('davhar.floor_number')
                    ->label('Давхрын дугаар')
                    ->sortable()
                    ->placeholder('—'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Үүсгэсэн огноо')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('korpus_id')
                    ->label('Блок')
                    ->relationship('korpus', 'name'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Хаалга нэмэх')
                    ->icon('heroicon-s-plus'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
