<?php

namespace App\Services;

use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use App\Models\Toot;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Service for managing the interactive door numbering system
 * 
 * This service handles:
 * - Automatic floor generation when Orcs are created
 * - Automatic door numbering based on configuration
 * - Hierarchical configuration (Korpus → Orc → Floor → Door)
 */
class InteractiveDoorManagementService
{
    /**
     * Generate floors for an Orc based on Korpus configuration
     *
     * @param Orc $orc
     * @return array
     */
    public function generateFloorsForOrc(Orc $orc): array
    {
        $korpus = $orc->korpus;
        $floorsCount = $orc->floors_count_override ?? $korpus->floors_count;
        
        Log::info('Generating floors for Orc', [
            'orc_id' => $orc->id,
            'korpus_id' => $korpus->id,
            'floors_count' => $floorsCount
        ]);

        $createdFloors = [];
        
        for ($floorNumber = 1; $floorNumber <= $floorsCount; $floorNumber++) {
            // Check if floor already exists
            $existingFloor = Davhar::where('orc_id', $orc->id)
                ->where('floor_number', $floorNumber)
                ->first();
                
            if (!$existingFloor) {
                $davhar = Davhar::create([
                    'orc_id' => $orc->id,
                    'number' => (string)$floorNumber,
                    'order' => $floorNumber,
                    'floor_number' => $floorNumber,
                    'doors_count' => $korpus->doors_per_floor
                ]);
                
                $createdFloors[] = $davhar;
                
                Log::info('Created floor for Orc', [
                    'davhar_id' => $davhar->id,
                    'floor_number' => $floorNumber,
                    'doors_count' => $korpus->doors_per_floor
                ]);
            }
        }
        
        return $createdFloors;
    }

    /**
     * Generate door numbers for a Korpus based on numbering type
     *
     * @param Korpus $korpus
     * @param bool $regenerateExisting
     * @return array
     */
    public function generateDoorNumbers(Korpus $korpus, bool $regenerateExisting = false): array
    {
        Log::info('Starting door number generation', [
            'korpus_id' => $korpus->id,
            'numbering_type' => $korpus->numbering_type,
            'regenerate_existing' => $regenerateExisting
        ]);

        return DB::transaction(function () use ($korpus, $regenerateExisting) {
            switch ($korpus->numbering_type) {
                case 1:
                    return $this->generateType1DoorNumbers($korpus, $regenerateExisting);
                case 2:
                    return $this->generateType2DoorNumbers($korpus, $regenerateExisting);
                case 3:
                    return $this->generateType3DoorNumbers($korpus, $regenerateExisting);
                default:
                    throw new \InvalidArgumentException("Unsupported numbering type: {$korpus->numbering_type}");
            }
        });
    }

    /**
     * Type 1: Korpus-wise consecutive numbering
     */
    protected function generateType1DoorNumbers(Korpus $korpus, bool $regenerateExisting): array
    {
        $created = 0;
        $updated = 0;
        $currentNumber = 1;

        foreach ($korpus->orcs()->orderBy('number')->get() as $orc) {
            foreach ($orc->davhars()->orderBy('order')->get() as $davhar) {
                $doorsCount = $davhar->doors_count;
                
                for ($i = 0; $i < $doorsCount; $i++) {
                    $doorNumber = $currentNumber++;
                    
                    $existingToot = Toot::where('korpus_id', $korpus->id)
                        ->where('davhar_id', $davhar->id)
                        ->where('number', $doorNumber)
                        ->first();

                    if ($existingToot) {
                        if ($regenerateExisting) {
                            $existingToot->update(['number' => $doorNumber]);
                            $updated++;
                        }
                    } else {
                        Toot::create([
                            'korpus_id' => $korpus->id,
                            'davhar_id' => $davhar->id,
                            'number' => $doorNumber
                        ]);
                        $created++;
                    }
                }
            }
        }

        return [
            'success' => true,
            'type' => 1,
            'created' => $created,
            'updated' => $updated
        ];
    }

    /**
     * Type 2: Orc-wise consecutive numbering
     */
    protected function generateType2DoorNumbers(Korpus $korpus, bool $regenerateExisting): array
    {
        $created = 0;
        $updated = 0;

        foreach ($korpus->orcs()->orderBy('number')->get() as $orc) {
            $currentNumber = 1;
            
            foreach ($orc->davhars()->orderBy('order')->get() as $davhar) {
                $doorsCount = $davhar->doors_count;
                
                for ($i = 0; $i < $doorsCount; $i++) {
                    $doorNumber = $currentNumber++;
                    
                    $existingToot = Toot::where('korpus_id', $korpus->id)
                        ->where('davhar_id', $davhar->id)
                        ->where('number', $doorNumber)
                        ->first();

                    if ($existingToot) {
                        if ($regenerateExisting) {
                            $existingToot->update(['number' => $doorNumber]);
                            $updated++;
                        }
                    } else {
                        Toot::create([
                            'korpus_id' => $korpus->id,
                            'davhar_id' => $davhar->id,
                            'number' => $doorNumber
                        ]);
                        $created++;
                    }
                }
            }
        }

        return [
            'success' => true,
            'type' => 2,
            'created' => $created,
            'updated' => $updated
        ];
    }

    /**
     * Type 3: Floor-wise consecutive numbering with digit multiplier
     */
    protected function generateType3DoorNumbers(Korpus $korpus, bool $regenerateExisting): array
    {
        $created = 0;
        $updated = 0;
        $digitMultiplier = $korpus->digit_multiplier;

        foreach ($korpus->orcs()->orderBy('number')->get() as $orc) {
            foreach ($orc->davhars()->orderBy('order')->get() as $davhar) {
                $floorNumber = $davhar->floor_number ?? $davhar->order;
                $doorsCount = $davhar->doors_count;
                
                for ($i = 1; $i <= $doorsCount; $i++) {
                    $doorNumber = ($floorNumber * $digitMultiplier) + $i;
                    
                    $existingToot = Toot::where('korpus_id', $korpus->id)
                        ->where('davhar_id', $davhar->id)
                        ->where('number', $doorNumber)
                        ->first();

                    if ($existingToot) {
                        if ($regenerateExisting) {
                            $existingToot->update(['number' => $doorNumber]);
                            $updated++;
                        }
                    } else {
                        Toot::create([
                            'korpus_id' => $korpus->id,
                            'davhar_id' => $davhar->id,
                            'number' => $doorNumber
                        ]);
                        $created++;
                    }
                }
            }
        }

        return [
            'success' => true,
            'type' => 3,
            'created' => $created,
            'updated' => $updated
        ];
    }

    /**
     * Update floors count for an Orc and regenerate floors if needed
     *
     * @param Orc $orc
     * @param int $newFloorsCount
     * @return array
     */
    public function updateOrcFloorsCount(Orc $orc, int $newFloorsCount): array
    {
        Log::info('Updating floors count for Orc', [
            'orc_id' => $orc->id,
            'new_floors_count' => $newFloorsCount
        ]);

        return DB::transaction(function () use ($orc, $newFloorsCount) {
            $orc->update(['floors_count_override' => $newFloorsCount]);
            
            // Remove excess floors if reducing count
            $currentFloorsCount = $orc->davhars()->count();
            if ($newFloorsCount < $currentFloorsCount) {
                $floorsToRemove = $orc->davhars()
                    ->where('floor_number', '>', $newFloorsCount)
                    ->get();
                    
                foreach ($floorsToRemove as $floor) {
                    $floor->delete(); // This will cascade delete doors
                }
            }
            
            // Add new floors if increasing count
            if ($newFloorsCount > $currentFloorsCount) {
                $this->generateFloorsForOrc($orc);
            }
            
            return [
                'success' => true,
                'orc_id' => $orc->id,
                'floors_count' => $newFloorsCount
            ];
        });
    }

    /**
     * Update doors count for a specific floor
     *
     * @param Davhar $davhar
     * @param int $newDoorsCount
     * @return array
     */
    public function updateFloorDoorsCount(Davhar $davhar, int $newDoorsCount): array
    {
        Log::info('Updating doors count for floor', [
            'davhar_id' => $davhar->id,
            'new_doors_count' => $newDoorsCount
        ]);

        return DB::transaction(function () use ($davhar, $newDoorsCount) {
            $davhar->update(['doors_count' => $newDoorsCount]);
            
            // Remove excess doors if reducing count
            $currentDoorsCount = $davhar->toots()->count();
            if ($newDoorsCount < $currentDoorsCount) {
                $doorsToRemove = $davhar->toots()
                    ->orderBy('number', 'desc')
                    ->take($currentDoorsCount - $newDoorsCount)
                    ->get();
                    
                foreach ($doorsToRemove as $door) {
                    $door->delete();
                }
            }
            
            // Regenerate door numbers for this floor
            $korpus = $davhar->orc->korpus;
            $this->regenerateFloorDoorNumbers($davhar, $korpus);
            
            return [
                'success' => true,
                'davhar_id' => $davhar->id,
                'doors_count' => $newDoorsCount
            ];
        });
    }

    /**
     * Regenerate door numbers for a specific floor
     */
    protected function regenerateFloorDoorNumbers(Davhar $davhar, Korpus $korpus): void
    {
        // Delete existing doors for this floor
        $davhar->toots()->delete();
        
        // Generate new doors based on numbering type
        switch ($korpus->numbering_type) {
            case 1:
                // For Type 1, we need to regenerate all doors in the Korpus
                $this->generateType1DoorNumbers($korpus, true);
                break;
            case 2:
                // For Type 2, we need to regenerate all doors in the Orc
                $this->regenerateOrcDoorNumbers($davhar->orc, $korpus);
                break;
            case 3:
                // For Type 3, we can regenerate just this floor
                $this->regenerateSingleFloorDoorNumbers($davhar, $korpus);
                break;
        }
    }

    /**
     * Regenerate door numbers for a specific Orc (Type 2)
     */
    protected function regenerateOrcDoorNumbers(Orc $orc, Korpus $korpus): void
    {
        $currentNumber = 1;
        
        foreach ($orc->davhars()->orderBy('order')->get() as $davhar) {
            $doorsCount = $davhar->doors_count;
            
            for ($i = 0; $i < $doorsCount; $i++) {
                Toot::create([
                    'korpus_id' => $korpus->id,
                    'davhar_id' => $davhar->id,
                    'number' => $currentNumber++
                ]);
            }
        }
    }

    /**
     * Regenerate door numbers for a single floor (Type 3)
     */
    protected function regenerateSingleFloorDoorNumbers(Davhar $davhar, Korpus $korpus): void
    {
        $floorNumber = $davhar->floor_number ?? $davhar->order;
        $digitMultiplier = $korpus->digit_multiplier;
        $doorsCount = $davhar->doors_count;
        
        for ($i = 1; $i <= $doorsCount; $i++) {
            $doorNumber = ($floorNumber * $digitMultiplier) + $i;
            
            Toot::create([
                'korpus_id' => $korpus->id,
                'davhar_id' => $davhar->id,
                'number' => $doorNumber
            ]);
        }
    }
}
