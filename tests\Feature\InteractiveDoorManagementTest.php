<?php

namespace Tests\Feature;

use App\Models\Sukh;
use App\Models\Bair;
use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use App\Models\Toot;
use App\Services\InteractiveDoorManagementService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class InteractiveDoorManagementTest extends TestCase
{
    use RefreshDatabase;

    protected InteractiveDoorManagementService $doorService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->doorService = app(InteractiveDoorManagementService::class);
    }

    /** @test */
    public function it_can_generate_floors_for_orc()
    {
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'numbering_type' => 1,
            'floors_count' => 3,
            'doors_per_floor' => 6
        ]);

        $orc = Orc::create([
            'korpus_id' => $korpus->id,
            'number' => '1'
        ]);

        $createdFloors = $this->doorService->generateFloorsForOrc($orc);

        $this->assertCount(3, $createdFloors);
        $this->assertEquals(3, $orc->davhars()->count());

        // Check floor properties
        $floors = $orc->davhars()->orderBy('order')->get();
        for ($i = 0; $i < 3; $i++) {
            $this->assertEquals($i + 1, $floors[$i]->floor_number);
            $this->assertEquals($i + 1, $floors[$i]->order);
            $this->assertEquals(6, $floors[$i]->doors_count);
        }
    }

    /** @test */
    public function it_can_generate_type1_door_numbers()
    {
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'numbering_type' => 1,
            'floors_count' => 2,
            'doors_per_floor' => 3
        ]);

        // Create Orcs and floors
        $orc1 = Orc::create(['korpus_id' => $korpus->id, 'number' => '1']);
        $orc2 = Orc::create(['korpus_id' => $korpus->id, 'number' => '2']);

        $this->doorService->generateFloorsForOrc($orc1);
        $this->doorService->generateFloorsForOrc($orc2);

        // Generate door numbers
        $result = $this->doorService->generateDoorNumbers($korpus);

        $this->assertTrue($result['success']);
        $this->assertEquals(1, $result['type']);
        $this->assertEquals(12, $result['created']); // 2 orcs * 2 floors * 3 doors

        // Verify consecutive numbering across all floors
        $allDoors = Toot::where('korpus_id', $korpus->id)->orderBy('number')->get();
        $this->assertCount(12, $allDoors);
        
        for ($i = 0; $i < 12; $i++) {
            $this->assertEquals($i + 1, $allDoors[$i]->number);
        }
    }

    /** @test */
    public function it_can_generate_type2_door_numbers()
    {
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'numbering_type' => 2,
            'floors_count' => 2,
            'doors_per_floor' => 3
        ]);

        // Create Orcs and floors
        $orc1 = Orc::create(['korpus_id' => $korpus->id, 'number' => '1']);
        $orc2 = Orc::create(['korpus_id' => $korpus->id, 'number' => '2']);

        $this->doorService->generateFloorsForOrc($orc1);
        $this->doorService->generateFloorsForOrc($orc2);

        // Generate door numbers
        $result = $this->doorService->generateDoorNumbers($korpus);

        $this->assertTrue($result['success']);
        $this->assertEquals(2, $result['type']);

        // Verify each Orc has independent numbering starting from 1
        $orc1Doors = Toot::whereIn('davhar_id', $orc1->davhars->pluck('id'))
            ->orderBy('number')->pluck('number')->toArray();
        $orc2Doors = Toot::whereIn('davhar_id', $orc2->davhars->pluck('id'))
            ->orderBy('number')->pluck('number')->toArray();

        $this->assertEquals([1, 2, 3, 4, 5, 6], $orc1Doors);
        $this->assertEquals([1, 2, 3, 4, 5, 6], $orc2Doors);
    }

    /** @test */
    public function it_can_generate_type3_door_numbers()
    {
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'numbering_type' => 3,
            'digit_multiplier' => 100,
            'floors_count' => 2,
            'doors_per_floor' => 3
        ]);

        $orc = Orc::create(['korpus_id' => $korpus->id, 'number' => '1']);
        $this->doorService->generateFloorsForOrc($orc);

        // Generate door numbers
        $result = $this->doorService->generateDoorNumbers($korpus);

        $this->assertTrue($result['success']);
        $this->assertEquals(3, $result['type']);

        // Verify floor-based numbering (floor 1: 101-103, floor 2: 201-203)
        $floor1Doors = Toot::where('davhar_id', $orc->davhars->where('floor_number', 1)->first()->id)
            ->orderBy('number')->pluck('number')->toArray();
        $floor2Doors = Toot::where('davhar_id', $orc->davhars->where('floor_number', 2)->first()->id)
            ->orderBy('number')->pluck('number')->toArray();

        $this->assertEquals([101, 102, 103], $floor1Doors);
        $this->assertEquals([201, 202, 203], $floor2Doors);
    }

    /** @test */
    public function it_can_update_orc_floors_count()
    {
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'numbering_type' => 1,
            'floors_count' => 2,
            'doors_per_floor' => 3
        ]);

        $orc = Orc::create(['korpus_id' => $korpus->id, 'number' => '1']);
        $this->doorService->generateFloorsForOrc($orc);

        $this->assertEquals(2, $orc->davhars()->count());

        // Update floors count to 4
        $result = $this->doorService->updateOrcFloorsCount($orc, 4);

        $this->assertTrue($result['success']);
        $this->assertEquals(4, $orc->fresh()->davhars()->count());
        $this->assertEquals(4, $orc->floors_count_override);
    }

    /** @test */
    public function it_can_update_floor_doors_count()
    {
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'numbering_type' => 1,
            'floors_count' => 1,
            'doors_per_floor' => 3
        ]);

        $orc = Orc::create(['korpus_id' => $korpus->id, 'number' => '1']);
        $this->doorService->generateFloorsForOrc($orc);
        $davhar = $orc->davhars()->first();

        $this->assertEquals(3, $davhar->doors_count);

        // Update doors count to 5
        $result = $this->doorService->updateFloorDoorsCount($davhar, 5);

        $this->assertTrue($result['success']);
        $this->assertEquals(5, $davhar->fresh()->doors_count);
    }

    /** @test */
    public function it_respects_orc_floors_count_override()
    {
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'numbering_type' => 1,
            'floors_count' => 2,
            'doors_per_floor' => 3
        ]);

        $orc = Orc::create([
            'korpus_id' => $korpus->id,
            'number' => '1',
            'floors_count_override' => 5
        ]);

        $createdFloors = $this->doorService->generateFloorsForOrc($orc);

        // Should create 5 floors (override) instead of 2 (korpus default)
        $this->assertCount(5, $createdFloors);
        $this->assertEquals(5, $orc->davhars()->count());
    }
}
