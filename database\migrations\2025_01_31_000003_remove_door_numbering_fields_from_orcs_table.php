<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orcs', function (Blueprint $table) {
            // Remove old door numbering fields
            $table->dropColumn([
                'begin_toot_number',
                'end_toot_number'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orcs', function (Blueprint $table) {
            // Restore old door numbering fields
            $table->integer('begin_toot_number')->nullable();
            $table->integer('end_toot_number')->nullable();
        });
    }
};
