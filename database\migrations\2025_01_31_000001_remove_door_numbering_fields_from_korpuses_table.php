<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('korpuses', function (Blueprint $table) {
            // Remove old door numbering fields
            $table->dropColumn([
                'begin_toot_number',
                'end_toot_number',
                'numbering_type',
                'digit_multiplier'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('korpuses', function (Blueprint $table) {
            // Restore old door numbering fields
            $table->integer('begin_toot_number')->default(1);
            $table->integer('end_toot_number')->default(1);
            $table->integer('numbering_type')->default(1)
                ->comment('1=Korpus-wise, 2=Orc-wise, 3=Floor-wise');
            $table->integer('digit_multiplier')->default(100)
                ->comment('Type 3: 10 (2-digit), 100 (3-digit), 1000 (4-digit)');
        });
    }
};
