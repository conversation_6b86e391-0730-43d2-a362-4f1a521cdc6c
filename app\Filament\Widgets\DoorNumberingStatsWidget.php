<?php

namespace App\Filament\Widgets;

use App\Models\Korpus;
use App\Models\Toot;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class DoorNumberingStatsWidget extends BaseWidget
{
    protected static ?int $sort = 3;
    
    protected static ?string $pollingInterval = '60s';

    protected function getStats(): array
    {
        try {
            // Get overall statistics
            $totalKorpuses = Korpus::whereNotNull('numbering_type')->count();
            $totalToots = Toot::count();
            
            // Calculate expected doors based on Korpus configurations
            $expectedDoorsData = $this->calculateExpectedDoors();
            $expectedDoors = $expectedDoorsData['total'];
            
            // Calculate completion percentage
            $completionPercentage = $expectedDoors > 0 
                ? round(($totalToots / $expectedDoors) * 100, 1) 
                : 0;
            
            // Get numbering type distribution
            $typeDistribution = $this->getNumberingTypeDistribution();
            
            return [
                Stat::make('Нийт блок', $totalKorpuses)
                    ->description('Дугаарлалттай блокууд')
                    ->descriptionIcon('heroicon-o-building-office-2')
                    ->color('primary'),
                    
                Stat::make('Нийт хаалга', "{$totalToots} / {$expectedDoors}")
                    ->description("{$completionPercentage}% бүрэн")
                    ->descriptionIcon('heroicon-o-key')
                    ->color($completionPercentage >= 80 ? 'success' : ($completionPercentage >= 50 ? 'warning' : 'danger')),
                    
                Stat::make('Дугаарлалтын бүрэн байдал', "{$completionPercentage}%")
                    ->description('Системийн бүрэн байдал')
                    ->descriptionIcon($completionPercentage >= 80 ? 'heroicon-o-check-circle' : 'heroicon-o-clock')
                    ->color($completionPercentage >= 80 ? 'success' : ($completionPercentage >= 50 ? 'warning' : 'danger')),
                    
                Stat::make('Дугаарлалтын төрлүүд', $this->formatTypeDistribution($typeDistribution))
                    ->description('Төрлийн тархалт')
                    ->descriptionIcon('heroicon-o-chart-pie')
                    ->color('info'),
            ];
        } catch (\Exception $e) {
            return [
                Stat::make('Хаалганы дугаарлалт', 'Алдаа')
                    ->description('Статистик ачаалахад алдаа гарлаа')
                    ->descriptionIcon('heroicon-o-exclamation-triangle')
                    ->color('danger'),
            ];
        }
    }
    
    /**
     * Calculate expected doors based on Korpus configurations
     */
    protected function calculateExpectedDoors(): array
    {
        $korpuses = Korpus::with(['orcs.davhars'])->whereNotNull('numbering_type')->get();
        $total = 0;
        $breakdown = [];
        
        foreach ($korpuses as $korpus) {
            $korpusExpected = 0;
            
            foreach ($korpus->orcs as $orc) {
                foreach ($orc->davhars as $davhar) {
                    $korpusExpected += $davhar->doors_count ?? 0;
                }
            }
            
            $total += $korpusExpected;
            $breakdown[$korpus->id] = $korpusExpected;
        }
        
        return [
            'total' => $total,
            'breakdown' => $breakdown
        ];
    }
    
    /**
     * Get numbering type distribution
     */
    protected function getNumberingTypeDistribution(): array
    {
        return Korpus::whereNotNull('numbering_type')
            ->select('numbering_type', DB::raw('count(*) as count'))
            ->groupBy('numbering_type')
            ->pluck('count', 'numbering_type')
            ->toArray();
    }
    
    /**
     * Format type distribution for display
     */
    protected function formatTypeDistribution(array $distribution): string
    {
        $formatted = [];
        
        foreach ($distribution as $type => $count) {
            $typeName = match($type) {
                1 => 'Т1',
                2 => 'Т2', 
                3 => 'Т3',
                default => 'Т?'
            };
            $formatted[] = "{$typeName}: {$count}";
        }
        
        return empty($formatted) ? 'Байхгүй' : implode(', ', $formatted);
    }
}
