<?php

namespace App\Http\Controllers;

use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use App\Services\InteractiveDoorManagementService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

/**
 * Controller for interactive door management system
 */
class InteractiveDoorController extends Controller
{
    protected InteractiveDoorManagementService $doorService;

    public function __construct(InteractiveDoorManagementService $doorService)
    {
        $this->doorService = $doorService;
    }

    /**
     * Update Korpus door configuration
     *
     * @param Request $request
     * @param int $korpusId
     * @return JsonResponse
     */
    public function updateKorpusConfiguration(Request $request, int $korpusId): JsonResponse
    {
        $request->validate([
            'numbering_type' => 'required|integer|in:1,2,3',
            'digit_multiplier' => 'required_if:numbering_type,3|integer|in:10,100,1000',
            'floors_count' => 'required|integer|min:1|max:50',
            'doors_per_floor' => 'required|integer|min:1|max:20'
        ]);

        $korpus = Korpus::findOrFail($korpusId);

        Log::info('Updating Korpus door configuration', [
            'korpus_id' => $korpusId,
            'data' => $request->only(['numbering_type', 'digit_multiplier', 'floors_count', 'doors_per_floor'])
        ]);

        $korpus->update($request->only([
            'numbering_type',
            'digit_multiplier',
            'floors_count',
            'doors_per_floor'
        ]));

        return response()->json([
            'success' => true,
            'message' => 'Korpus configuration updated successfully',
            'data' => $korpus->fresh()
        ]);
    }

    /**
     * Generate floors for all Orcs in a Korpus
     *
     * @param Request $request
     * @param int $korpusId
     * @return JsonResponse
     */
    public function generateFloorsForKorpus(Request $request, int $korpusId): JsonResponse
    {
        $korpus = Korpus::findOrFail($korpusId);
        
        Log::info('Generating floors for Korpus', ['korpus_id' => $korpusId]);

        $results = [];
        foreach ($korpus->orcs as $orc) {
            $createdFloors = $this->doorService->generateFloorsForOrc($orc);
            $results[] = [
                'orc_id' => $orc->id,
                'floors_created' => count($createdFloors)
            ];
        }

        return response()->json([
            'success' => true,
            'message' => 'Floors generated successfully',
            'data' => $results
        ]);
    }

    /**
     * Generate door numbers for a Korpus
     *
     * @param Request $request
     * @param int $korpusId
     * @return JsonResponse
     */
    public function generateDoorNumbers(Request $request, int $korpusId): JsonResponse
    {
        $request->validate([
            'regenerate_existing' => 'boolean'
        ]);

        $korpus = Korpus::findOrFail($korpusId);
        $regenerateExisting = $request->input('regenerate_existing', false);

        Log::info('Generating door numbers for Korpus', [
            'korpus_id' => $korpusId,
            'regenerate_existing' => $regenerateExisting
        ]);

        try {
            $result = $this->doorService->generateDoorNumbers($korpus, $regenerateExisting);

            return response()->json([
                'success' => true,
                'message' => 'Door numbers generated successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('Error generating door numbers', [
                'korpus_id' => $korpusId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate door numbers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update floors count for a specific Orc
     *
     * @param Request $request
     * @param int $orcId
     * @return JsonResponse
     */
    public function updateOrcFloorsCount(Request $request, int $orcId): JsonResponse
    {
        $request->validate([
            'floors_count' => 'required|integer|min:1|max:50'
        ]);

        $orc = Orc::findOrFail($orcId);
        $floorsCount = $request->input('floors_count');

        Log::info('Updating Orc floors count', [
            'orc_id' => $orcId,
            'floors_count' => $floorsCount
        ]);

        try {
            $result = $this->doorService->updateOrcFloorsCount($orc, $floorsCount);

            return response()->json([
                'success' => true,
                'message' => 'Orc floors count updated successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating Orc floors count', [
                'orc_id' => $orcId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update Orc floors count',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update doors count for a specific floor
     *
     * @param Request $request
     * @param int $davharId
     * @return JsonResponse
     */
    public function updateFloorDoorsCount(Request $request, int $davharId): JsonResponse
    {
        $request->validate([
            'doors_count' => 'required|integer|min:1|max:20'
        ]);

        $davhar = Davhar::findOrFail($davharId);
        $doorsCount = $request->input('doors_count');

        Log::info('Updating floor doors count', [
            'davhar_id' => $davharId,
            'doors_count' => $doorsCount
        ]);

        try {
            $result = $this->doorService->updateFloorDoorsCount($davhar, $doorsCount);

            return response()->json([
                'success' => true,
                'message' => 'Floor doors count updated successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating floor doors count', [
                'davhar_id' => $davharId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update floor doors count',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get Korpus door configuration and statistics
     *
     * @param int $korpusId
     * @return JsonResponse
     */
    public function getKorpusConfiguration(int $korpusId): JsonResponse
    {
        $korpus = Korpus::with(['orcs.davhars.toots'])->findOrFail($korpusId);

        $statistics = [
            'orcs_count' => $korpus->orcs->count(),
            'total_floors' => $korpus->orcs->sum(function ($orc) {
                return $orc->davhars->count();
            }),
            'total_doors' => $korpus->orcs->sum(function ($orc) {
                return $orc->davhars->sum(function ($davhar) {
                    return $davhar->toots->count();
                });
            })
        ];

        $orcsData = $korpus->orcs->map(function ($orc) {
            return [
                'id' => $orc->id,
                'number' => $orc->number,
                'floors_count_override' => $orc->floors_count_override,
                'floors' => $orc->davhars->map(function ($davhar) {
                    return [
                        'id' => $davhar->id,
                        'number' => $davhar->number,
                        'floor_number' => $davhar->floor_number,
                        'doors_count' => $davhar->doors_count,
                        'actual_doors_count' => $davhar->toots->count(),
                        'door_numbers' => $davhar->toots->pluck('number')->sort()->values()
                    ];
                })
            ];
        });

        return response()->json([
            'success' => true,
            'data' => [
                'korpus' => $korpus->only([
                    'id', 'name', 'numbering_type', 'digit_multiplier', 
                    'floors_count', 'doors_per_floor'
                ]),
                'statistics' => $statistics,
                'orcs' => $orcsData
            ]
        ]);
    }

    /**
     * Generate floors for a specific Orc
     *
     * @param int $orcId
     * @return JsonResponse
     */
    public function generateFloorsForOrc(int $orcId): JsonResponse
    {
        $orc = Orc::findOrFail($orcId);

        Log::info('Generating floors for specific Orc', ['orc_id' => $orcId]);

        try {
            $createdFloors = $this->doorService->generateFloorsForOrc($orc);

            return response()->json([
                'success' => true,
                'message' => 'Floors generated successfully for Orc',
                'data' => [
                    'orc_id' => $orc->id,
                    'floors_created' => count($createdFloors),
                    'floors' => $createdFloors
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error generating floors for Orc', [
                'orc_id' => $orcId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate floors for Orc',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
