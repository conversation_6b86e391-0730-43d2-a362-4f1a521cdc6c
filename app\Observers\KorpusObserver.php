<?php

namespace App\Observers;

use App\Models\Korpus;
use App\Services\KorpusSyncService;
use App\Services\InteractiveDoorManagementService;
use Illuminate\Support\Facades\Log;

class KorpusObserver
{
    protected KorpusSyncService $korpusSyncService;
    protected InteractiveDoorManagementService $doorManagementService;

    public function __construct(
        KorpusSyncService $korpusSyncService,
        InteractiveDoorManagementService $doorManagementService
    ) {
        $this->korpusSyncService = $korpusSyncService;
        $this->doorManagementService = $doorManagementService;
    }

    /**
     * Handle the Korpus "created" event.
     *
     * @param Korpus $korpus
     * @return void
     */
    public function created(Korpus $korpus): void
    {
        Log::info('KorpusObserver: Korpus created event triggered', [
            'korpus_id' => $korpus->id,
            'korpus_name' => $korpus->name
        ]);

        // Sync with CVSecurity service
        $this->korpusSyncService->syncCreate($korpus);
    }

    /**
     * Handle the Korpus "updated" event.
     *
     * @param Korpus $korpus
     * @return void
     */
    public function updated(Korpus $korpus): void
    {
        // Skip sync if only the code field was updated (to avoid infinite loops)
        if ($korpus->wasChanged(Korpus::CODE) && count($korpus->getChanges()) === 1) {
            Log::debug('KorpusObserver: Skipping sync for code-only update', [
                'korpus_id' => $korpus->id
            ]);
            return;
        }

        Log::info('KorpusObserver: Korpus updated event triggered', [
            'korpus_id' => $korpus->id,
            'korpus_name' => $korpus->name,
            'changed_fields' => array_keys($korpus->getChanges())
        ]);

        // Sync with CVSecurity service
        $this->korpusSyncService->syncUpdate($korpus);

        // Check if door numbering related fields were changed
        $doorNumberingFields = ['numbering_type', 'digit_multiplier', 'begin_toot_number', 'end_toot_number'];
        $changedFields = array_keys($korpus->getChanges());
        $hasRelevantChanges = !empty(array_intersect($doorNumberingFields, $changedFields));

        if ($hasRelevantChanges && $this->hasAnyDavhars($korpus)) {
            Log::info('KorpusObserver: Triggering door numbering recalculation', [
                'korpus_id' => $korpus->id,
                'changed_fields' => $changedFields
            ]);

            try {
                $this->doorManagementService->generateDoorNumbers($korpus, true);
            } catch (\Exception $e) {
                Log::error('KorpusObserver: Error during door numbering calculation', [
                    'korpus_id' => $korpus->id,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Handle the Korpus "deleted" event.
     *
     * @param Korpus $korpus
     * @return void
     */
    public function deleted(Korpus $korpus): void
    {
        Log::info('KorpusObserver: Korpus deleted event triggered', [
            'korpus_id' => $korpus->id,
            'korpus_name' => $korpus->name,
            'cv_code' => $korpus->code
        ]);

        // Sync with CVSecurity service
        $this->korpusSyncService->syncDelete($korpus);
    }

    /**
     * Handle the Korpus "retrieved" event.
     * This can be used for read operations if needed.
     *
     * @param Korpus $korpus
     * @return void
     */
    public function retrieved(Korpus $korpus): void
    {
        // Optionally sync read operations
        // Uncomment the line below if you want to sync on every read
        // $this->korpusSyncService->syncRead($korpus);
    }

    /**
     * Handle the Korpus "restoring" event.
     *
     * @param Korpus $korpus
     * @return void
     */
    public function restoring(Korpus $korpus): void
    {
        Log::info('KorpusObserver: Korpus restoring event triggered', [
            'korpus_id' => $korpus->id,
            'korpus_name' => $korpus->name
        ]);
    }

    /**
     * Handle the Korpus "restored" event.
     *
     * @param Korpus $korpus
     * @return void
     */
    public function restored(Korpus $korpus): void
    {
        Log::info('KorpusObserver: Korpus restored event triggered', [
            'korpus_id' => $korpus->id,
            'korpus_name' => $korpus->name
        ]);

        // Treat restoration as creation for CVSecurity
        $this->korpusSyncService->syncCreate($korpus);
    }

    /**
     * Handle the Korpus "force deleted" event.
     *
     * @param Korpus $korpus
     * @return void
     */
    public function forceDeleted(Korpus $korpus): void
    {
        Log::info('KorpusObserver: Korpus force deleted event triggered', [
            'korpus_id' => $korpus->id,
            'korpus_name' => $korpus->name,
            'cv_code' => $korpus->code
        ]);

        // Sync with CVSecurity service
        $this->korpusSyncService->syncDelete($korpus);
    }

    /**
     * Check if a Korpus has any Davhars
     *
     * @param Korpus $korpus
     * @return bool
     */
    protected function hasAnyDavhars(Korpus $korpus): bool
    {
        return $korpus->orcs()
            ->whereHas('davhars')
            ->exists();
    }
}
