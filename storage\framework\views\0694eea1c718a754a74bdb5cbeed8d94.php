
<div class="fi-fo-field-wrp">
    <div class="fi-fo-field-wrp-label">
        <label class="fi-fo-field-wrp-label-text text-sm font-medium leading-6 text-gray-950 dark:text-white">
            Хаалганы дугаарлалтын урьдчилан харах
        </label>
    </div>
    <div class="space-y-4">
        <!--[if BLOCK]><![endif]--><?php if(isset($previewData['empty'])): ?>
            <div class="text-sm text-gray-500 italic">
                Тохиргоо сонгосны дараа урьдчилан харах боломжтой болно
            </div>
        <?php elseif(isset($previewData['error'])): ?>
            <div class="text-sm text-red-600">
                <?php echo e($previewData['error']); ?>

            </div>
        <?php else: ?>
            
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-2">
                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    <h3 class="text-lg font-semibold text-blue-800">Урьдчилан харах</h3>
                </div>
                <p class="text-sm text-blue-700 mb-2"><?php echo e($previewData['description']); ?></p>
                <div class="text-xs text-blue-600 bg-blue-100 rounded px-2 py-1 inline-block">
                    <strong>Анхаар:</strong> Энэ нь зөвхөн урьдчилан харах зориулалттай. Бодит давхар болон хаалганууд хадгалсны дараа үүснэ.
                </div>
            </div>

            
            <div class="space-y-4">
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $previewData['orcs']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $orc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800">
                                Орц <?php echo e($orc['number']); ?>

                            </h4>
                            <div class="text-sm text-gray-600">
                                <?php echo e($orc['floors_count']); ?> давхар
                                <!--[if BLOCK]><![endif]--><?php if(isset($orc['total_doors'])): ?>
                                    • <?php echo e($orc['total_doors']); ?> хаалга
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>

                        
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $orc['floors']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $floor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="bg-white border border-gray-300 rounded-md p-3">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium text-gray-700">
                                            Давхар <?php echo e($floor['number']); ?>

                                        </span>
                                        <span class="text-xs text-gray-500">
                                            <?php echo e($floor['door_count']); ?> хаалга
                                        </span>
                                    </div>
                                    <div class="text-lg font-bold text-blue-600 bg-blue-50 rounded px-2 py-1 text-center">
                                        <?php echo e($floor['door_range']); ?>

                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="font-semibold text-green-800">
                        Нийт: <?php echo e($previewData['total_doors']); ?> хаалга
                    </span>
                </div>
                <!--[if BLOCK]><![endif]--><?php if($previewData['type'] == 3): ?>
                    <div class="text-sm text-green-700 mt-1">
                        Цифрийн формат: <?php echo e($previewData['digit_multiplier'] == 10 ? '2 цифр' : ($previewData['digit_multiplier'] == 100 ? '3 цифр' : '4 цифр')); ?>

                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH D:\workspaces\iot\sdoor\api\resources\views/filament/components/door-numbering-preview.blade.php ENDPATH**/ ?>