<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('korpuses', function (Blueprint $table) {
            $table->integer('numbering_type')->default(1)->after('end_toot_number')
                ->comment('1=Korpus-wise, 2=Orc-wise, 3=Floor-wise');
            $table->integer('digit_multiplier')->default(100)->after('numbering_type')
                ->comment('Type 3: 10 (2-digit), 100 (3-digit), 1000 (4-digit)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('korpuses', function (Blueprint $table) {
            $table->dropColumn(['numbering_type', 'digit_multiplier']);
        });
    }
};
