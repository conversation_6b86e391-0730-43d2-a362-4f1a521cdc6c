<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('korpuses', function (Blueprint $table) {
            // Add new interactive door management fields
            $table->integer('numbering_type')->default(1)->after('order')
                ->comment('1=Type1, 2=Type2, 3=Type3 door numbering');
            $table->integer('digit_multiplier')->default(100)->after('numbering_type')
                ->comment('Type 3: 10 (2-digit), 100 (3-digit), 1000 (4-digit)');
            $table->integer('floors_count')->default(1)->after('digit_multiplier')
                ->comment('Default number of floors for each Orc in this Korpus');
            $table->integer('doors_per_floor')->default(6)->after('floors_count')
                ->comment('Default number of doors per floor');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('korpuses', function (Blueprint $table) {
            $table->dropColumn([
                'numbering_type',
                'digit_multiplier',
                'floors_count',
                'doors_per_floor'
            ]);
        });
    }
};
