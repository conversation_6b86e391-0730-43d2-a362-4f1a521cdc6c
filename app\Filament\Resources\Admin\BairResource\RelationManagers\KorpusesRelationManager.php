<?php

namespace App\Filament\Resources\Admin\BairResource\RelationManagers;

use App\Models\Korpus;
use App\Models\Orc;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Validation\Rules\Unique;

class KorpusesRelationManager extends RelationManager
{
    protected static string $relationship = 'korpuses';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make(Korpus::NAME)
                            ->label('Нэр')
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule) {
                                    $bairId = $this->getOwnerRecord()->id;
                                    return $rule->where('bair_id', $bairId);
                                }
                            )
                            ->required(),
                        Forms\Components\TextInput::make(Korpus::ORDER)
                            ->required()
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule) {
                                    $bairId = $this->getOwnerRecord()->id;
                                    return $rule->where('bair_id', $bairId);
                                }
                            )
                            ->label('Дараалал')
                            ->minValue(1)
                            ->numeric()
                            ->maxValue(9999),
                        Forms\Components\Select::make(Korpus::NUMBERING_TYPE)
                            ->label('Дугаарлалтын төрөл')
                            ->options([
                                1 => 'Төрөл 1: Блокийн дугаарлалт',
                                2 => 'Төрөл 2: Орцны дугаарлалт',
                                3 => 'Төрөл 3: Давхрын дугаарлалт'
                            ])
                            ->default(1)
                            ->required()
                            ->live(),
                        Forms\Components\Select::make(Korpus::DIGIT_MULTIPLIER)
                            ->label('Цифрийн формат')
                            ->options([
                                10 => '2 цифр (11, 12, 13...)',
                                100 => '3 цифр (101, 102, 103...)',
                                1000 => '4 цифр (1001, 1002, 1003...)'
                            ])
                            ->default(100)
                            ->visible(fn ($get) => $get(Korpus::NUMBERING_TYPE) == 3)
                            ->required(),
                        Forms\Components\TextInput::make(Korpus::FLOORS_COUNT)
                            ->label('Давхрын тоо')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(50)
                            ->default(1)
                            ->required(),
                        Forms\Components\TextInput::make(Korpus::DOORS_PER_FLOOR)
                            ->label('Давхар тутмын хаалганы тоо')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(20)
                            ->default(6)
                            ->required(),
                    ]),
                    Forms\Components\Repeater::make(Korpus::RELATION_ORCS)
                        ->relationship()
                        ->schema([
                            Forms\Components\TextInput::make(ORC::NUMBER)
                                ->label('Орцны дугаар')
                                ->unique(
                                    ignoreRecord: true,
                                    modifyRuleUsing: function (Unique $rule, callable $get) {
                                        $korpusId = $get('../../id');
                                        return $rule->where('korpus_id', $korpusId);
                                    }
                                )
                                ->numeric()
                                ->required(),
                            Forms\Components\TextInput::make(ORC::FLOORS_COUNT_OVERRIDE)
                                ->label('Давхрын тоо (тусгай)')
                                ->numeric()
                                ->minValue(1)
                                ->maxValue(50)
                                ->placeholder('Блокийн үндсэн тохиргоог ашиглах'),
                        ])
                        ->reorderable(false)
                        ->columns(3),
            ])->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('number')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Korpus::NAME)->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Korpus::ORDER)->label('Дараалал')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-s-plus'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
