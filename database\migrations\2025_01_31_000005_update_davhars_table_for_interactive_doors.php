<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('davhars', function (Blueprint $table) {
            // Remove old door numbering fields
            $table->dropColumn([
                'begin_toot_number',
                'end_toot_number'
            ]);
            
            // Add new interactive door management fields
            $table->integer('doors_count')->default(6)->after('floor_number')
                ->comment('Number of doors on this specific floor (overrides Korpus default)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('davhars', function (Blueprint $table) {
            // Remove new fields
            $table->dropColumn('doors_count');
            
            // Restore old door numbering fields
            $table->integer('begin_toot_number')->default(0);
            $table->integer('end_toot_number')->default(0);
        });
    }
};
