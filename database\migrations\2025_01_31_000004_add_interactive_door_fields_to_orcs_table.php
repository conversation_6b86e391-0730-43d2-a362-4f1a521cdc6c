<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orcs', function (Blueprint $table) {
            // Add new interactive door management fields
            $table->integer('floors_count_override')->nullable()->after('number')
                ->comment('Override the default floors count from Korpus for this specific Orc');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orcs', function (Blueprint $table) {
            $table->dropColumn('floors_count_override');
        });
    }
};
